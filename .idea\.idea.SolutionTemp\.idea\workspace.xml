<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile>WinFormsAppIssueTime/WinFormsAppIssueTime.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3183116c-e0ea-4a1c-881d-7de75d6c17b4" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="30M53Ia10NzZURbQaLYeoVTbaoP" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;.NET 项目.WinFormsAppIssueTime.executor&quot;: &quot;Debug&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="WinFormsAppIssueTime" type="DotNetProject" factoryName=".NET Project">
      <option name="EXE_PATH" value="" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="AUTO_ATTACH_CHILDREN" value="0" />
      <option name="MIXED_MODE_DEBUG" value="0" />
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/WinFormsAppIssueTime/WinFormsAppIssueTime.csproj" />
      <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
      <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
      <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
      <option name="PROJECT_KIND" value="DotNetCore" />
      <option name="PROJECT_TFM" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3183116c-e0ea-4a1c-881d-7de75d6c17b4" name="更改" comment="" />
      <created>1753424401422</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753424401422</updated>
      <workItem from="1753424402963" duration="6984000" />
      <workItem from="1753448031415" duration="4763000" />
      <workItem from="1753454788510" duration="1487000" />
      <workItem from="1753482682131" duration="2685000" />
      <workItem from="1753493860426" duration="5119000" />
      <workItem from="1753521884198" duration="3600000" />
      <workItem from="1753534534392" duration="1534000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="1" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="3" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>