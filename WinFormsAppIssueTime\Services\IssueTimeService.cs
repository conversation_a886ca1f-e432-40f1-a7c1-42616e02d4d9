﻿using Microsoft.Extensions.Logging;
using WinFormsAppIssueTime.Interfaces;
using WinFormsAppIssueTime.Models;

namespace WinFormsAppIssueTime.Services;

/// <summary>
/// 时间发放服务实现类
/// 负责处理发放时间的业务逻辑，包括数据的创建、查询、删除等操作
/// </summary>
public class IssueTimeService : IIssueTimeService
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<IssueTimeService> _logger;

    /// <summary>
    /// FreeSql ORM 实例，用于数据库操作
    /// </summary>
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 缓存的发放时间对象，避免频繁查询数据库
    /// </summary>
    private IssueTime? _cachedCurrentIssueTime;

    /// <summary>
    /// 缓存更新的锁对象，确保线程安全
    /// </summary>
    private readonly object _cacheLock = new();

    #endregion

    #region 构造函数

    /// <summary>
    /// 构造函数，通过依赖注入获取 FreeSql 实例
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="freeSql">FreeSql 实例</param>
    public IssueTimeService(ILogger<IssueTimeService> logger, IFreeSql freeSql)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _freeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));
    }

    #endregion

    #region 基础查询方法

    /// <summary>
    /// 获取发放记录的总条数
    /// </summary>
    /// <returns>数据库中发放记录的总数量</returns>
    public Task<long> GetIssueTimeCountAsync()
    {
        // 使用 FreeSql 查询 IssueTime 表的记录总数
        return _freeSql.Select<IssueTime>().CountAsync();
    }

    /// <summary>
    /// 获取发放记录的第一条
    /// 用于检查数据库中是否存在数据以及获取年份信息
    /// </summary>
    /// <returns>第一条发放记录</returns>
    public async Task<IssueTime> GetFirstIssueTimeAsync()
    {
        // 获取数据库中的第一条发放记录
        return await _freeSql.Select<IssueTime>().FirstAsync();
    }

    /// <summary>
    /// 清空所有发放记录
    /// </summary>
    /// <returns>删除操作的异步任务</returns>
    public Task ClearIssueTimeAsync()
    {
        // 删除 IssueTime 表中的所有记录
        return _freeSql.Delete<IssueTime>().ExecuteAffrowsAsync();
    }

    #endregion

    #region 数据创建和管理方法

    /// <summary>
    /// 创建发放记录
    /// 生成一整年的发放时间数据，每天203次，每次间隔5分钟
    /// </summary>
    public async Task<IssueTime> CreateIssueTimeAsync(DateTime issueTimeYear)
    {
        // 取出指定年份的第一条发放记录
        var firstIssueTime = await _freeSql.Select<IssueTime>()
            .Where(x => x.OpenTime.Year == issueTimeYear.Year)
            .FirstAsync();
        if (firstIssueTime != null)
        {
            return firstIssueTime;
        }

        // 开始创建发放记录,从1月到12月遍历整年
        List<IssueTime> issueTimeList = new List<IssueTime>();
        int index = 0; // 全局序号计数器
        for (int month = 1; month <= 12; month++)
        {
            // 获取当前月份的天数（自动处理闰年二月）
            int daysInMonth = DateTime.DaysInMonth(issueTimeYear.Year, month);

            // 遍历该月的每一天
            for (int day = 1; day <= daysInMonth; day++)
            {
                // 每天从6:55开始（7:00-5分钟）
                DateTime time = new DateTime(issueTimeYear.Year, month, day, 7, 0, 0).AddMinutes(-5);

                // 每天发放203次，每次间隔5分钟
                for (int i = 1; i <= 203; i++)
                {
                    DateTime openTime = time.AddMinutes(5); // 开放时间
                    DateTime closeTime = openTime.AddMinutes(5).AddSeconds(-10); // 关闭时间（开放后5分钟, 关闭前10秒）
                    index++; // 全局序号递增

                    // 生成6位序号（左侧补0）
                    string indexStr = index.ToString().PadLeft(6, '0');

                    // 生成发放编号：民国年份+6位序号
                    string issueTime = $"{issueTimeYear.Year - 1911}{indexStr}";

                    // 创建发放时间对象
                    IssueTime issueTimeModel = new IssueTime()
                    {
                        Issue = issueTime,
                        OpenTime = openTime,
                        CloseTime = closeTime
                    };
                    issueTimeList.Add(issueTimeModel);

                    // 顺延时间到下一个发放时间点
                    time = openTime;
                }
            }
        }

        // 调用FreeSql批量插入发放记录
        await _freeSql.Insert(issueTimeList).ExecuteAffrowsAsync();
        return issueTimeList[0];
    }

    #endregion

    #region 业务查询方法

    /// <summary>
    /// 获取当前时间对应的发放时间段，如果当前时间不在任何时间段内，则返回最近的未来时间段
    /// </summary>
    /// <returns>当前或下一个发放时间段</returns>
    public async Task<IssueTime?> GetCurrentOrNextIssueTimeAsync()
    {
        var now = DateTime.Now;
        lock (_cacheLock)
        {
            // 检查缓存是否有效
            if (_cachedCurrentIssueTime != null && IsCacheValid(_cachedCurrentIssueTime, now))
            {
                // 缓存有效，直接返回缓存的数据
                return _cachedCurrentIssueTime;
            }
        }

        // 缓存无效或不存在，重新查询数据库,首先查找当前时间是否在某个发放时间段内（OpenTime <= now <= CloseTime）
        var currentIssueTime = await _freeSql.Select<IssueTime>()
            .Where(x => x.OpenTime <= now && x.CloseTime >= now)
            .FirstAsync();

        if (currentIssueTime != null)
        {
            _logger.LogInformation($"当前时间 {now:yyyy-MM-dd HH:mm:ss} 属于发放时间段: {currentIssueTime.Issue} ({currentIssueTime.OpenTime:yyyy-MM-dd HH:mm:ss} - {currentIssueTime.CloseTime:yyyy-MM-dd HH:mm:ss})");

            // 更新缓存
            lock (_cacheLock)
            {
                _cachedCurrentIssueTime = currentIssueTime;
            }

            _logger.LogInformation($"缓存已更新: {_cachedCurrentIssueTime?.Issue} ({_cachedCurrentIssueTime?.OpenTime:HH:mm:ss} - {_cachedCurrentIssueTime?.CloseTime:HH:mm:ss})");
            return _cachedCurrentIssueTime;
        }

        // 如果当前时间不在任何发放时间段内，查找最近的未来时间段
        var nextIssueTime = await _freeSql.Select<IssueTime>()
            .Where(x => x.OpenTime > now)
            .OrderBy(x => x.OpenTime)
            .FirstAsync();

        if (nextIssueTime != null)
        {
            _logger.LogInformation($"当前时间 {now:yyyy-MM-dd HH:mm:ss} 不在任何发放时间段内，最近的未来时间段: {nextIssueTime.Issue} ({nextIssueTime.OpenTime:yyyy-MM-dd HH:mm:ss} - {nextIssueTime.CloseTime:yyyy-MM-dd HH:mm:ss})");

            // 更新缓存
            lock (_cacheLock)
            {
                _cachedCurrentIssueTime = currentIssueTime;
            }

            _logger.LogInformation($"缓存已更新: {_cachedCurrentIssueTime?.Issue} ({_cachedCurrentIssueTime?.OpenTime:HH:mm:ss} - {_cachedCurrentIssueTime?.CloseTime:HH:mm:ss})");
            return _cachedCurrentIssueTime;
        }

        DateTime nextYear = new DateTime((int)(_cachedCurrentIssueTime?.OpenTime.Year + 1)!, 1, 1);
        var newFirstIssueTime = await CreateIssueTimeAsync(nextYear);

        // 更新缓存
        lock (_cacheLock)
        {
            _cachedCurrentIssueTime = newFirstIssueTime;
        }

        return _cachedCurrentIssueTime;
    }

    #endregion

    #region 缓存优化方法

    /// <summary>
    /// 清除缓存
    /// 强制下次查询时重新从数据库获取数据
    /// </summary>
    public void ClearCache()
    {
        lock (_cacheLock)
        {
            _cachedCurrentIssueTime = null;
            _logger.LogInformation("发放时间缓存已清除");
        }
    }

    /// <summary>
    /// 检查缓存是否仍然有效
    /// </summary>
    /// <param name="cachedIssueTime">缓存的发放时间对象</param>
    /// <param name="currentTime">当前时间</param>
    /// <returns>如果缓存有效返回true，否则返回false</returns>
    private bool IsCacheValid(IssueTime cachedIssueTime, DateTime currentTime)
    {
        // 如果当前时间在发放时间段内，缓存有效
        if (currentTime >= cachedIssueTime.OpenTime && currentTime <= cachedIssueTime.CloseTime)
        {
            return true;
        }

        // 如果当前时间小于开放时间，且距离开放时间还有超过1分钟，缓存有效
        // 这样可以避免在即将开放时频繁查询数据库
        if (currentTime < cachedIssueTime.OpenTime)
        {
            var timeToOpen = cachedIssueTime.OpenTime - currentTime;
            return timeToOpen.TotalMinutes > 1;
        }

        // 其他情况缓存无效（当前时间已超过关闭时间）
        return false;
    }

    #endregion
}