﻿using WinFormsAppIssueTime.Models;

namespace WinFormsAppIssueTime.Interfaces;

/// <summary>
/// 时间发放服务接口
/// 提供发放时间的创建、查询、删除等功能
/// </summary>
public interface IIssueTimeService
{
    /// <summary>
    /// 获取发放记录的总条数
    /// </summary>
    /// <returns>发放记录总数</returns>
    Task<long> GetIssueTimeCountAsync();

    /// <summary>
    /// 获取发放记录的第一条
    /// </summary>
    /// <returns>第一条发放记录</returns>
    Task<IssueTime> GetFirstIssueTimeAsync();

    /// <summary>
    /// 创建发放记录
    /// 生成一年的发放时间数据并保存到数据库
    /// </summary>
    Task CreateIssueTimeAsync();

    /// <summary>
    /// 清空所有发放记录
    /// </summary>
    /// <returns>删除操作的异步任务</returns>
    Task ClearIssueTimeAsync();

    /// <summary>
    /// 获取当前时间对应的发放时间段，如果当前时间不在任何时间段内，则返回最近的未来时间段
    /// </summary>
    /// <returns>当前或下一个发放时间段，如果没有找到则返回null</returns>
    Task<IssueTime?> GetCurrentOrNextIssueTimeAsync();

    /// <summary>
    /// 获取当前时间对应的发放时间段（带缓存优化）
    /// 使用缓存机制减少数据库查询，只有当时间超出缓存范围时才重新查询数据库
    /// </summary>
    /// <returns>当前或下一个发放时间段，如果没有找到则返回null</returns>
    Task<IssueTime?> GetCurrentOrNextIssueTimeWithCacheAsync();

    /// <summary>
    /// 清除缓存
    /// 强制下次查询时重新从数据库获取数据
    /// </summary>
    void ClearCache();
}